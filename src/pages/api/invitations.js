import { adminAuth, adminDb } from '../../lib/firebaseAdmin';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req, res) {
  try {
    // Verify the user is authenticated
    const token = req.headers.authorization?.split('Bearer ')[1];
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decodedToken = await adminAuth.verifyIdToken(token);
    const userId = decodedToken.uid;

    // Get user data to check if they're admin
    const userDoc = await adminDb.collection('users').doc(userId).get();
    if (!userDoc.exists || userDoc.data().role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    if (req.method === 'POST') {
      // Create new invitation
      const { email, role = 'user', expiresInDays = 7 } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'Email is required' });
      }

      const invitationCode = uuidv4().substring(0, 8).toUpperCase();
      const expiresAt = new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000);

      const invitation = {
        code: invitationCode,
        email,
        role,
        createdBy: userId,
        createdAt: new Date(),
        expiresAt,
        isUsed: false
      };

      const docRef = await adminDb.collection('invitations').add(invitation);

      return res.status(201).json({
        id: docRef.id,
        ...invitation
      });

    } else if (req.method === 'GET') {
      // Get all invitations
      const invitationsSnapshot = await adminDb
        .collection('invitations')
        .orderBy('createdAt', 'desc')
        .get();

      const invitations = invitationsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        expiresAt: doc.data().expiresAt.toDate()
      }));

      return res.status(200).json(invitations);

    } else if (req.method === 'DELETE') {
      // Delete invitation
      const { invitationId } = req.query;

      if (!invitationId) {
        return res.status(400).json({ error: 'Invitation ID is required' });
      }

      await adminDb.collection('invitations').doc(invitationId).delete();

      return res.status(200).json({ message: 'Invitation deleted successfully' });

    } else {
      res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }

  } catch (error) {
    console.error('Invitations API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
